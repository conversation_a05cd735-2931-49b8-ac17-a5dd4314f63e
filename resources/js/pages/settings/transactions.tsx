import { Head, usePage } from '@inertiajs/react';
import { useEffect, useState } from 'react';
import axios from 'axios';

import HeadingSmall from '@/components/heading-small';
import { type BreadcrumbItem, type SharedData } from '@/types';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    TableFooter,
    TableCaption
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';

import AppLayout from '@/layouts/app-layout';
import SettingsLayout from '@/layouts/settings/layout';
import Cookies from "js-cookie";

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Transaction history',
        href: '/settings/transactions',
    },
];

interface Transaction {
    id: number;
    type: 'deposit' | 'withdrawal';
    wallet_address: string;
    amount: string;
    currency: string;
    tx_hash: string;
    status: 'pending' | 'confirmed' | 'failed';
    created_at: string;
}

interface PaginatedResponse {
    data: Transaction[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

export default function Transactions() {
    const { auth } = usePage<SharedData>().props;
    const [transactions, setTransactions] = useState<Transaction[]>([]);
    const [loading, setLoading] = useState(true);
    const [pagination, setPagination] = useState({
        currentPage: 1,
        lastPage: 1,
        perPage: 10,
        total: 0,
        from: 0,
        to: 0
    });

    const fetchTransactions = async (page = 1) => {
        setLoading(true);

        try {
            const response = await axios.get(`/api/transactions?page=${page}`, {
                withCredentials: true,
                headers: {
                    'Authorization': `Bearer ${Cookies.get('user_token')}`,
                    'Content-Type': 'application/json'
                }
            });

            const paginatedData = response.data as PaginatedResponse;
            setTransactions(paginatedData.data);
            setPagination({
                currentPage: paginatedData.current_page,
                lastPage: paginatedData.last_page,
                perPage: paginatedData.per_page,
                total: paginatedData.total,
                from: paginatedData.from || 0,
                to: paginatedData.to || 0
            });
        } catch (error) {
            console.error('Failed to fetch transactions:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchTransactions();
    }, []);

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'confirmed':
                return <Badge className="bg-green-500">Confirmed</Badge>;
            case 'pending':
                return <Badge className="bg-yellow-500">Pending</Badge>;
            case 'failed':
                return <Badge className="bg-red-500">Failed</Badge>;
            default:
                return <Badge>{status}</Badge>;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Transaction History" />

            <SettingsLayout>
                <div className="space-y-6">
                    <HeadingSmall
                        title="Transaction History"
                        description="View your recent deposits and withdrawals"
                    />

                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Transactions</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {loading ? (
                                <div className="flex justify-center p-4">Loading transactions...</div>
                            ) : transactions.length === 0 ? (
                                <div className="text-center p-4">No transactions found</div>
                            ) : (
                                <>
                                    <Table>
                                        <TableCaption>A list of your recent transactions</TableCaption>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Type</TableHead>
                                                <TableHead>Amount</TableHead>
                                                <TableHead>Currency</TableHead>
                                                <TableHead>Status</TableHead>
                                                <TableHead>Date</TableHead>
                                                <TableHead>Transaction Hash</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {transactions.map((tx) => (
                                                <TableRow key={tx.id}>
                                                    <TableCell className="capitalize">{tx.type}</TableCell>
                                                    <TableCell>{tx.amount}</TableCell>
                                                    <TableCell>{tx.currency || auth.user.currency}</TableCell>
                                                    <TableCell>{getStatusBadge(tx.status)}</TableCell>
                                                    <TableCell>{new Date(tx.created_at).toLocaleDateString()}</TableCell>
                                                    <TableCell className="truncate max-w-[150px]">
                                                        <a
                                                            href={`https://sepolia.etherscan.io/tx/${tx.tx_hash}`}
                                                            target="_blank"
                                                            rel="noopener noreferrer"
                                                            className="text-blue-500 hover:underline"
                                                        >
                                                            {tx.tx_hash.substring(0, 10)}...
                                                        </a>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                        <TableFooter>
                                            <TableRow>
                                                <TableCell colSpan={6}>
                                                    <div className="flex items-center justify-between">
                                                        <div className="text-sm text-muted-foreground">
                                                            Showing {pagination.from} to {pagination.to} of {pagination.total} transactions
                                                        </div>
                                                        <div className="flex items-center space-x-2">
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                onClick={() => fetchTransactions(pagination.currentPage - 1)}
                                                                disabled={pagination.currentPage === 1 || loading}
                                                            >
                                                                <ChevronLeft className="h-4 w-4" />
                                                                <span className="sr-only">Previous Page</span>
                                                            </Button>
                                                            <div className="text-sm">
                                                                Page {pagination.currentPage} of {pagination.lastPage}
                                                            </div>
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                onClick={() => fetchTransactions(pagination.currentPage + 1)}
                                                                disabled={pagination.currentPage === pagination.lastPage || loading}
                                                            >
                                                                <ChevronRight className="h-4 w-4" />
                                                                <span className="sr-only">Next Page</span>
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        </TableFooter>
                                    </Table>
                                </>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </SettingsLayout>
        </AppLayout>
    );
}
