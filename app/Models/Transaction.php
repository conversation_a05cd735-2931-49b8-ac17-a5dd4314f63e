<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Transaction extends Model {

    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'wallet_address',
        'amount',
        'currency',
        'tx_hash',
        'status',
        'meta'
    ];

    protected $casts = [
        'meta' => 'array', // This allows <PERSON><PERSON> to store and retrieve arrays/objects as JSON
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
