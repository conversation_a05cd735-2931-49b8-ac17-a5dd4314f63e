<?php

namespace App\Http\Controllers;

use App\Jobs\ProcessTransaction;
use Illuminate\Http\Request;

class TransactionController extends Controller
{

    public function index()
    {
        $transactions = auth()->user()->transactions()
            ->latest()
            ->paginate(10);

        return response()->json($transactions);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'wallet_address' => 'required|string',
            'amount' => 'required|numeric|min:0',
            'tx_hash' => 'required|string|unique:transactions,tx_hash',
            'type' => 'required|in:deposit,withdrawal',
        ]);

        \Log::info('TransactionController@store', $validated);

        $tx = $request->user()->transactions()->create([
            'type' => $validated['type'], // Use the validated type instead of hardcoding 'deposit'
            'wallet_address' => $validated['wallet_address'],
            'amount' => $validated['amount'],
            'tx_hash' => $validated['tx_hash'],
            'status' => 'pending',
        ]);

        \Log::info('transaction created', $tx->toArray());

        // Queue the transaction for processing
        ProcessTransaction::dispatch($tx);

        return response()->json(['success' => true, 'transaction' => $tx->toArray()]);
    }
}

