<?php

namespace App\Services;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;

class CubeiaOperatorApiService
{
    protected $client;
    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        $apiKey =  config('services.cubeia.api_key');
        $url = config('services.cubeia.base_url') . "/operator-api/rest";

        \Log::info('CubeiaOperatorApiService initialized', [
            'url' => $url,
            'apiKey' => $apiKey,
        ]);

        $this->client = Http::withHeaders([
                "x-api-key" => $apiKey,
                "Content-Type" => "application/json",
        ])->baseUrl($url)->acceptJson()->timeout(10);
    }

    /**
     * Get user balance from Cubeia
     *
     * @param string $userId The user ID.
     * @param string $currency The currency code, e.g., "USD"
     * @return float|null
     * @throws \Exception
     */

    public function getUserBalance(string $userId, string $currency): ?float
    {
        $cacheKey = "cubeia.balance.{$userId}.{$currency}";
        return Cache::remember($cacheKey, 300, function () use ($userId, $currency) {
            try {
                $url = "/account/{$userId}/{$currency}";
                $response = $this->client->get($url);

                if ($response->status() === 200) {
                    $body = $response->json();
                    return floatval($body['balance']['amount'] ?? 0);
                }

                if ($response->status() === 404) {
                    \Log::warning("Cubeia: User '{$userId}' not found.");
                    return null;
                }

                if ($response->status() === 401) {
                    \Log::error('Cubeia: Invalid API key.');
                    throw new \Exception('Invalid API key');
                }

                \Log::error("Cubeia: Unexpected response ({$response->status()})");
                return null;

            } catch (\Throwable $e) {
                \Log::error("Cubeia API request failed: " . $e->getMessage());
                return null;
            }
        });
    }

    public function refreshUserBalance(string $userId, string $currency): ?float
    {
        $cacheKey = "cubeia.balance.{$userId}.{$currency}";

        try {
            $url = "/account/{$userId}/{$currency}";
            $response = $this->client->get($url);

            if ($response->status() === 200) {
                $body = $response->json();
                $balance = floatval($body['balance']['amount'] ?? 0);
                \Log::info('Updating the cache for User: ' . $userId . ', Currency: ' . $currency . ', Balance: ' . $balance);
                Cache::put($cacheKey, $balance, 300); // update cache
                return $balance;
            }

            if ($response->status() === 404) {
                \Log::warning("Cubeia: User '{$userId}' not found.");
                Cache::forget($cacheKey);
                return null;
            }

            if ($response->status() === 401) {
                \Log::error('Cubeia: Invalid API key.');
                throw new \Exception('Invalid API key');
            }

            \Log::error("Cubeia: Unexpected response ({$response->status()})");
            return null;

        } catch (\Throwable $e) {
            \Log::error("Cubeia API request failed: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Sends a credit transaction to the Cubeia Operator API.
     *
     * @param int $userId
     * @param string $currency
     * @param float $amount
     * @param string $externalId
     * @param string $comment
     * @return array
     * @throws \Exception
     */
    public function creditTransaction(int $userId, string $currency, float $amount, string $externalId, string $comment)
    {
        $endpoint = "/account/{$userId}/{$currency}/transaction";

        $payload = [
            'source' => 'SYSTEM',
            'type' => 'CREDIT',
            'amount' => $amount,
            'externalId' => $externalId,
            'comment' => $comment,
        ];

        \Log::debug('Cubeia API - creditTransaction - payload: ', [
            'endpoint' => $endpoint,
            'payload' => $payload
        ]);


        $response = $this->client->post($endpoint, $payload);

        \Log::info('Cubeia API - creditTransaction - response: ' . json_encode($response->json()));

        if ($response->successful()) {
            $this->refreshUserBalance($userId, $currency);
            return $response->json();
        }

        if ($response->failed()) {
            // Handle the error as needed, e.g., log or throw an exception

            \Log::info('Cubeia API - creditTransaction - response failed: ' . json_encode($response->json()));
            throw new \Exception($response);
        }

        return $response->json();
    }
};
