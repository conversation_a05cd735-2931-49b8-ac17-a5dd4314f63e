<?php

namespace Database\Factories;

use App\Models\Transaction;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class TransactionFactory extends Factory
{
    protected $model = Transaction::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'type' => 'deposit',
            'wallet_address' => $this->faker->randomElement(['0x1234567890abcdef', '0x9876543210abcdef']),//ethereumAddress(),
            'amount' => $this->faker->randomFloat(6, 0.01, 5),
            'currency' => 'mETH',
            'tx_hash' => Str::random(66), // 0x-prefixed hash
            'status' => 'pending',
            'meta' => [],
        ];
    }
}
